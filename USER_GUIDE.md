# MoneyMind User Guide

Welcome to MoneyMind! This guide will help you get started with managing your personal finances effectively.

## Getting Started

### 1. Creating Your Account
1. Visit the MoneyMind application
2. Click "Sign Up" on the login page
3. Fill in your details:
   - Username (unique identifier)
   - Email address
   - Password (minimum 6 characters)
   - First and Last name
4. Click "Create Account"
5. You'll be automatically logged in to your dashboard

### 2. First Login
After creating your account, you'll see the main dashboard with:
- Financial overview cards showing your income, expenses, and net income
- Empty charts (these will populate as you add transactions)
- Quick access to add transactions and budgets

## Managing Transactions

### Adding Transactions
1. Navigate to the "Transactions" page
2. Click the "Add Transaction" button
3. Fill in the transaction details:
   - **Description**: What the transaction was for (e.g., "Grocery shopping")
   - **Amount**: The monetary value
   - **Type**: Choose "Income" or "Expense"
   - **Date**: When the transaction occurred
   - **Category**: Select from available categories
   - **Notes**: Optional additional details
4. Click "Add Transaction"

### Editing Transactions
1. Find the transaction in your list
2. Click the edit icon (pencil) next to the transaction
3. Modify the details as needed
4. Click "Update Transaction"

### Filtering Transactions
Use the filter options to find specific transactions:
- **Type**: Filter by income or expense
- **Category**: Show only transactions from specific categories
- **Date Range**: Set start and end dates
- **Search**: Find transactions by description

### Deleting Transactions
1. Find the transaction you want to delete
2. Click the delete icon (trash can)
3. Confirm the deletion in the popup dialog

## Managing Categories

### Default Categories
MoneyMind comes with pre-built categories:
- **Income**: Salary, Freelance, Investments, Business, Gifts, Other Income
- **Expense**: Food & Dining, Transportation, Shopping, Entertainment, Bills & Utilities, Healthcare, Education, Travel, Personal Care, Other Expense

### Creating Custom Categories
1. Go to the "Categories" page
2. Click "Add Category"
3. Fill in the details:
   - **Name**: Category name (e.g., "Pet Expenses")
   - **Type**: Income or Expense
   - **Icon**: Choose an emoji to represent the category
   - **Color**: Pick a color for visual identification
4. Click "Create Category"

### Managing Categories
- **Edit**: Click the edit icon to modify category details
- **Delete**: Remove custom categories (default categories cannot be deleted)
- **Activate/Deactivate**: Toggle category availability

## Budget Management

### Creating Budgets
1. Navigate to the "Budgets" page
2. Click "Create Budget"
3. Set up your budget:
   - **Name**: Descriptive name (e.g., "Monthly Food Budget")
   - **Category**: Choose which expense category to budget for
   - **Amount**: Set your spending limit
   - **Period**: Choose Monthly or Yearly
   - **Start Date**: When the budget period begins
   - **Alert Threshold**: Percentage at which you want to be warned (default: 80%)
   - **Notes**: Optional description
4. Click "Create Budget"

### Understanding Budget Status
Budgets are color-coded based on your spending:
- **Green (Good)**: Spending is below the alert threshold
- **Yellow (Warning)**: Spending has reached the alert threshold
- **Red (Over Budget)**: You've exceeded your budget amount

### Budget Progress Tracking
Each budget shows:
- **Budgeted Amount**: Your set limit
- **Amount Spent**: Current spending in the period
- **Remaining**: How much you have left
- **Progress Bar**: Visual representation of spending vs budget
- **Percentage Used**: Exact percentage of budget consumed

### Editing Budgets
1. Click the edit icon on any budget card
2. Modify the budget details
3. Click "Update Budget"

### Deleting Budgets
1. Click the delete icon on the budget card
2. Confirm the deletion

## Dashboard Overview

### Financial Summary Cards
- **Total Income**: Sum of all income transactions
- **Total Expenses**: Sum of all expense transactions
- **Net Income**: Income minus expenses

### Charts and Visualizations
- **Spending by Category**: Pie chart showing expense distribution
- **Monthly Trends**: Bar chart comparing income vs expenses over time
- **Budget Progress**: Visual representation of all active budgets

### Recent Transactions
Quick view of your latest 5 transactions with:
- Transaction details
- Category icons
- Amount and type indicators

### Budget Overview
Summary of your budget performance:
- Total budgeted amount
- Total spent across all budgets
- Remaining budget amounts
- Count of budgets by status (good, warning, over)

## Data Management

### Exporting Data
1. Access the Data Management section
2. Choose Export Data tab
3. Select what to export:
   - **Transactions**: Export as CSV or JSON
   - **Budgets**: Export as CSV or JSON
   - **Complete Backup**: Export everything as JSON
4. Click the appropriate export button
5. File will download automatically

### Importing Data
1. Go to Data Management → Import Data
2. Choose the type of data to import:
   - **Transactions**: Supports CSV and JSON
   - **Budgets**: Supports JSON only
3. Select your file using the file picker
4. Review the import results
5. Data will be automatically added to your account

### CSV Format for Transactions
When importing transactions via CSV, use this format:
```csv
Date,Description,Amount,Type,Category,Notes
2025-01-15,Grocery shopping,75.50,expense,Food & Dining,Weekly groceries
2025-01-16,Salary,3000.00,income,Salary,Monthly salary
```

## Tips for Effective Financial Management

### Best Practices
1. **Regular Updates**: Add transactions daily or weekly
2. **Accurate Categorization**: Use consistent categories for better insights
3. **Realistic Budgets**: Set achievable budget amounts
4. **Review Regularly**: Check your dashboard weekly
5. **Use Notes**: Add context to transactions for future reference

### Budget Setting Tips
- Start with 80% of what you think you can spend
- Review and adjust budgets monthly
- Set alerts at 70-80% to give yourself time to adjust
- Create separate budgets for different spending goals

### Category Organization
- Keep categories broad enough to be useful but specific enough to be meaningful
- Use consistent naming conventions
- Regularly review and clean up unused categories
- Consider seasonal categories for holiday spending

## Troubleshooting

### Common Issues

**Can't log in?**
- Check your email and password
- Ensure caps lock is off
- Try refreshing the page

**Transactions not showing?**
- Check your date filters
- Verify you're looking at the correct transaction type
- Refresh the page

**Budget not updating?**
- Ensure transactions are in the correct category
- Check that transaction dates fall within the budget period
- Verify the budget is active

**Charts not displaying?**
- Ensure you have transactions in your account
- Check that you have transactions in multiple categories
- Try refreshing the page

### Getting Help
If you encounter issues not covered in this guide:
1. Check the browser console for error messages
2. Try logging out and back in
3. Clear your browser cache
4. Contact support with specific error details

## Security and Privacy

### Account Security
- Use a strong, unique password
- Log out when using shared computers
- Don't share your login credentials

### Data Privacy
- Your financial data is stored securely
- Data is only accessible to your account
- Regular backups protect against data loss

### Session Management
- Sessions expire after inactivity for security
- You'll be prompted to log in again if your session expires
- Multiple devices can be logged in simultaneously

## Mobile Usage

MoneyMind is fully responsive and works great on mobile devices:
- Touch-friendly interface
- Optimized layouts for small screens
- All features available on mobile
- Swipe gestures for navigation

### Mobile Tips
- Use the mobile browser's "Add to Home Screen" feature
- Landscape mode provides more space for charts
- Pull down to refresh data on mobile

---

**Need more help?** Contact our support team or check the FAQ section for additional assistance.
