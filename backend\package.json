{"name": "backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.0.1", "express": "^5.1.0", "express-session": "^1.18.1", "mongoose": "^8.16.1", "passport": "^0.7.0", "passport-local": "^1.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}