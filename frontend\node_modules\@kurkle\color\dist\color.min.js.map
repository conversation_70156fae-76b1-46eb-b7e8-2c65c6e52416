{"version": 3, "file": "color.min.js", "sources": ["../src/byte.js", "../src/hex.js", "../src/hue.js", "../packed.js", "../src/names.js", "../src/rgb.js", "../src/srgb.js", "../src/color.js", "../src/index.esm.js", "../src/index.js"], "sourcesContent": ["/**\n * @packageDocumentation\n * @module utils\n */\n\n/**\n * Rounds decimal to nearest integer\n * @param {number} v - the number to round\n */\nexport function round(v) {\n  return v + 0.5 | 0;\n}\n\nexport const lim = (v, l, h) => Math.max(Math.min(v, h), l);\n\n/**\n * convert percent to byte 0..255\n * @param {number} v - 0..100\n */\nexport function p2b(v) {\n  return lim(round(v * 2.55), 0, 255);\n}\n\n/**\n * convert byte to percet 0..100\n * @param {number} v - 0..255\n */\nexport function b2p(v) {\n  return lim(round(v / 2.55), 0, 100);\n}\n\n/**\n * convert normalized to byte 0..255\n * @param {number} v - 0..1\n */\nexport function n2b(v) {\n  return lim(round(v * 255), 0, 255);\n}\n\n/**\n * convert byte to normalized 0..1\n * @param {number} v - 0..255\n */\nexport function b2n(v) {\n  return lim(round(v / 2.55) / 100, 0, 1);\n}\n\n/**\n * convert normalized to percent 0..100\n * @param {number} v - 0..1\n */\nexport function n2p(v) {\n  return lim(round(v * 100), 0, 100);\n}\n", "/**\n * @packageDocumentation\n * @module utils\n */\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * @hidden\n */\nconst map = {0: 0, 1: 1, 2: 2, 3: 3, 4: 4, 5: 5, 6: 6, 7: 7, 8: 8, 9: 9, A: 10, B: 11, C: 12, D: 13, E: 14, F: 15, a: 10, b: 11, c: 12, d: 13, e: 14, f: 15};\n\n/**\n * @hidden\n */\nconst hex = [...'0123456789ABCDEF'];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst h1 = b => hex[b & 0xF];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst h2 = b => hex[(b & 0xF0) >> 4] + hex[b & 0xF];\n\n/**\n * @param {number} b - byte\n * @hidden\n */\nconst eq = b => ((b & 0xF0) >> 4) === (b & 0xF);\n\n/**\n * @param {RGBA} v - the color\n * @hidden\n */\nconst isShort = v => eq(v.r) && eq(v.g) && eq(v.b) && eq(v.a);\n\n/**\n * Parse HEX to color\n * @param {string} str - the string\n */\nexport function hexParse(str) {\n  var len = str.length;\n  var ret;\n  if (str[0] === '#') {\n    if (len === 4 || len === 5) {\n      ret = {\n        r: 255 & map[str[1]] * 17,\n        g: 255 & map[str[2]] * 17,\n        b: 255 & map[str[3]] * 17,\n        a: len === 5 ? map[str[4]] * 17 : 255\n      };\n    } else if (len === 7 || len === 9) {\n      ret = {\n        r: map[str[1]] << 4 | map[str[2]],\n        g: map[str[3]] << 4 | map[str[4]],\n        b: map[str[5]] << 4 | map[str[6]],\n        a: len === 9 ? (map[str[7]] << 4 | map[str[8]]) : 255\n      };\n    }\n  }\n  return ret;\n}\n\nconst alpha = (a, f) => a < 255 ? f(a) : '';\n\n/**\n * Return HEX string from color\n * @param {RGBA} v - the color\n * @return {string|undefined}\n */\nexport function hexString(v) {\n  var f = isShort(v) ? h1 : h2;\n  return v\n    ? '#' + f(v.r) + f(v.g) + f(v.b) + alpha(v.a, f)\n    : undefined;\n}\n", "/**\n * @packageDocumentation\n * @module utils\n */\n\nimport {b2n, n2p, n2b, p2b} from './byte.js';\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * @hidden\n */\nconst HUE_RE = /^(hsla?|hwb|hsv)\\(\\s*([-+.e\\d]+)(?:deg)?[\\s,]+([-+.e\\d]+)%[\\s,]+([-+.e\\d]+)%(?:[\\s,]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\n\n/**\n * Converts hsl to rgb normalized\n * @url https://jsfiddle.net/Lamik/reuk63ay/91\n * @param {number} h - hue [0..360]\n * @param {number} s - saturation [0..1]\n * @param {number} l - lightness [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hsl2rgbn(h, s, l) {\n  const a = s * Math.min(l, 1 - l);\n  /**\n   * @param {number} n\n   */\n  const f = (n, k = (n + h / 30) % 12) => l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);\n  return [f(0), f(8), f(4)];\n}\n\n/**\n * Convert hsv to rgb normalized\n * @url https://jsfiddle.net/Lamik/Lr61wqub/15/\n * @param {number} h - hue [0..360]\n * @param {number} s - saturation [0..1]\n * @param {number} v - value [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hsv2rgbn(h, s, v) {\n  /**\n   * @param {number} n\n   */\n  const f = (n, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0);\n  return [f(5), f(3), f(1)];\n}\n\n/**\n * Convert hwb to rgb normalized\n * @param {number} h - hue [0..360]\n * @param {number} w - whiteness [0..1]\n * @param {number} b - blackness [0..1]\n * @returns {number[]} - [r, g, b] each normalized to [0..1]\n * @hidden\n */\nfunction hwb2rgbn(h, w, b) {\n  const rgb = hsl2rgbn(h, 1, 0.5);\n  let i;\n  if (w + b > 1) {\n    i = 1 / (w + b);\n    w *= i;\n    b *= i;\n  }\n  for (i = 0; i < 3; i++) {\n    rgb[i] *= 1 - w - b;\n    rgb[i] += w;\n  }\n  return rgb;\n}\n\nfunction hueValue(r, g, b, d, max) {\n  if (r === max) {\n    return ((g - b) / d) + (g < b ? 6 : 0);\n  }\n  if (g === max) {\n    return (b - r) / d + 2;\n  }\n  return (r - g) / d + 4;\n}\n\n/**\n * Convert rgb to hsl\n * @param {RGBA} v - the color\n * @returns {number[]} - [h, s, l]\n */\nexport function rgb2hsl(v) {\n  const range = 255;\n  const r = v.r / range;\n  const g = v.g / range;\n  const b = v.b / range;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const l = (max + min) / 2;\n  let h, s, d;\n  if (max !== min) {\n    d = max - min;\n    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);\n    h = hueValue(r, g, b, d, max);\n    h = h * 60 + 0.5;\n  }\n  return [h | 0, s || 0, l];\n}\n\n/**\n * @param {function} f\n * @param {number|number[]} a\n * @param {number} b\n * @param {number} c\n * @private\n * @hidden\n */\nfunction calln(f, a, b, c) {\n  return (\n    Array.isArray(a)\n      ? f(a[0], a[1], a[2])\n      : f(a, b, c)\n  ).map(n2b);\n}\n\n/**\n * Convert hsl to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [s] - saturation\n * @param {number} [l] - lightness\n * @returns {number[]}\n */\nexport function hsl2rgb(h, s, l) {\n  return calln(hsl2rgbn, h, s, l);\n}\n\n/**\n * Convert hwb to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [w] - whiteness\n * @param {number} [b] - blackness\n * @returns {number[]}\n */\nexport function hwb2rgb(h, w, b) {\n  return calln(hwb2rgbn, h, w, b);\n}\n\n/**\n * Convert hsv to rgb\n * @param {number|number[]} h - hue | [h, s, l]\n * @param {number} [s] - saturation\n * @param {number} [v] - value\n * @returns {number[]}\n */\nexport function hsv2rgb(h, s, v) {\n  return calln(hsv2rgbn, h, s, v);\n}\n\n/**\n * @param {number} h - the angle\n * @hidden\n */\nfunction hue(h) {\n  return (h % 360 + 360) % 360;\n}\n\n/**\n * Parse hsl/hsv/hwb color string\n * @param {string} str - hsl/hsv/hwb color string\n * @returns {RGBA} - the parsed color components\n */\nexport function hueParse(str) {\n  const m = HUE_RE.exec(str);\n  let a = 255;\n  let v;\n  if (!m) {\n    return;\n  }\n  // v is undefined\n  if (m[5] !== v) {\n    a = m[6] ? p2b(+m[5]) : n2b(+m[5]);\n  }\n  const h = hue(+m[2]);\n  const p1 = +m[3] / 100;\n  const p2 = +m[4] / 100;\n  if (m[1] === 'hwb') {\n    v = hwb2rgb(h, p1, p2);\n  } else if (m[1] === 'hsv') {\n    v = hsv2rgb(h, p1, p2);\n  } else {\n    v = hsl2rgb(h, p1, p2);\n  }\n  return {\n    r: v[0],\n    g: v[1],\n    b: v[2],\n    a: a\n  };\n}\n\n/**\n * Rotate the `v` color by `deg` degrees\n * @param {RGBA} v - the color\n * @param {number} deg - degrees to rotate\n */\nexport function rotate(v, deg) {\n  var h = rgb2hsl(v);\n  h[0] = hue(h[0] + deg);\n  h = hsl2rgb(h);\n  v.r = h[0];\n  v.g = h[1];\n  v.b = h[2];\n}\n\n/**\n * Return hsl(a) string from color components\n * @param {RGBA} v - the color\n * @return {string|undefined}\n */\nexport function hslString(v) {\n  if (!v) {\n    return;\n  }\n  const a = rgb2hsl(v);\n  const h = a[0];\n  const s = n2p(a[1]);\n  const l = n2p(a[2]);\n  return v.a < 255\n    ? `hsla(${h}, ${s}%, ${l}%, ${b2n(v.a)})`\n    : `hsl(${h}, ${s}%, ${l}%)`;\n}\n", "\nconst map = {\n\tx: 'dark',\n\tZ: 'light',\n\tY: 're',\n\tX: 'blu',\n\tW: 'gr',\n\tV: 'medium',\n\tU: 'slate',\n\tA: 'ee',\n\tT: 'ol',\n\tS: 'or',\n\tB: 'ra',\n\tC: 'lateg',\n\tD: 'ights',\n\tR: 'in',\n\tQ: 'turquois',\n\tE: 'hi',\n\tP: 'ro',\n\tO: 'al',\n\tN: 'le',\n\tM: 'de',\n\tL: 'yello',\n\tF: 'en',\n\tK: 'ch',\n\tG: 'arks',\n\tH: 'ea',\n\tI: 'ightg',\n\tJ: 'wh'\n};\nconst names = {\n\tOiceXe: 'f0f8ff',\n\tantiquewEte: 'faebd7',\n\taqua: 'ffff',\n\taquamarRe: '7fffd4',\n\tazuY: 'f0ffff',\n\tbeige: 'f5f5dc',\n\tbisque: 'ffe4c4',\n\tblack: '0',\n\tblanKedOmond: 'ffebcd',\n\tXe: 'ff',\n\tXeviTet: '8a2be2',\n\tbPwn: 'a52a2a',\n\tburlywood: 'deb887',\n\tcaMtXe: '5f9ea0',\n\tKartYuse: '7fff00',\n\tKocTate: 'd2691e',\n\tcSO: 'ff7f50',\n\tcSnflowerXe: '6495ed',\n\tcSnsilk: 'fff8dc',\n\tcrimson: 'dc143c',\n\tcyan: 'ffff',\n\txXe: '8b',\n\txcyan: '8b8b',\n\txgTMnPd: 'b8860b',\n\txWay: 'a9a9a9',\n\txgYF: '6400',\n\txgYy: 'a9a9a9',\n\txkhaki: 'bdb76b',\n\txmagFta: '8b008b',\n\txTivegYF: '556b2f',\n\txSange: 'ff8c00',\n\txScEd: '9932cc',\n\txYd: '8b0000',\n\txsOmon: 'e9967a',\n\txsHgYF: '8fbc8f',\n\txUXe: '483d8b',\n\txUWay: '2f4f4f',\n\txUgYy: '2f4f4f',\n\txQe: 'ced1',\n\txviTet: '9400d3',\n\tdAppRk: 'ff1493',\n\tdApskyXe: 'bfff',\n\tdimWay: '696969',\n\tdimgYy: '696969',\n\tdodgerXe: '1e90ff',\n\tfiYbrick: 'b22222',\n\tflSOwEte: 'fffaf0',\n\tfoYstWAn: '228b22',\n\tfuKsia: 'ff00ff',\n\tgaRsbSo: 'dcdcdc',\n\tghostwEte: 'f8f8ff',\n\tgTd: 'ffd700',\n\tgTMnPd: 'daa520',\n\tWay: '808080',\n\tgYF: '8000',\n\tgYFLw: 'adff2f',\n\tgYy: '808080',\n\thoneyMw: 'f0fff0',\n\thotpRk: 'ff69b4',\n\tRdianYd: 'cd5c5c',\n\tRdigo: '4b0082',\n\tivSy: 'fffff0',\n\tkhaki: 'f0e68c',\n\tlavFMr: 'e6e6fa',\n\tlavFMrXsh: 'fff0f5',\n\tlawngYF: '7cfc00',\n\tNmoncEffon: 'fffacd',\n\tZXe: 'add8e6',\n\tZcSO: 'f08080',\n\tZcyan: 'e0ffff',\n\tZgTMnPdLw: 'fafad2',\n\tZWay: 'd3d3d3',\n\tZgYF: '90ee90',\n\tZgYy: 'd3d3d3',\n\tZpRk: 'ffb6c1',\n\tZsOmon: 'ffa07a',\n\tZsHgYF: '20b2aa',\n\tZskyXe: '87cefa',\n\tZUWay: '778899',\n\tZUgYy: '778899',\n\tZstAlXe: 'b0c4de',\n\tZLw: 'ffffe0',\n\tlime: 'ff00',\n\tlimegYF: '32cd32',\n\tlRF: 'faf0e6',\n\tmagFta: 'ff00ff',\n\tmaPon: '800000',\n\tVaquamarRe: '66cdaa',\n\tVXe: 'cd',\n\tVScEd: 'ba55d3',\n\tVpurpN: '9370db',\n\tVsHgYF: '3cb371',\n\tVUXe: '7b68ee',\n\tVsprRggYF: 'fa9a',\n\tVQe: '48d1cc',\n\tVviTetYd: 'c71585',\n\tmidnightXe: '191970',\n\tmRtcYam: 'f5fffa',\n\tmistyPse: 'ffe4e1',\n\tmoccasR: 'ffe4b5',\n\tnavajowEte: 'ffdead',\n\tnavy: '80',\n\tTdlace: 'fdf5e6',\n\tTive: '808000',\n\tTivedBb: '6b8e23',\n\tSange: 'ffa500',\n\tSangeYd: 'ff4500',\n\tScEd: 'da70d6',\n\tpOegTMnPd: 'eee8aa',\n\tpOegYF: '98fb98',\n\tpOeQe: 'afeeee',\n\tpOeviTetYd: 'db7093',\n\tpapayawEp: 'ffefd5',\n\tpHKpuff: 'ffdab9',\n\tperu: 'cd853f',\n\tpRk: 'ffc0cb',\n\tplum: 'dda0dd',\n\tpowMrXe: 'b0e0e6',\n\tpurpN: '800080',\n\tYbeccapurpN: '663399',\n\tYd: 'ff0000',\n\tPsybrown: 'bc8f8f',\n\tPyOXe: '4169e1',\n\tsaddNbPwn: '8b4513',\n\tsOmon: 'fa8072',\n\tsandybPwn: 'f4a460',\n\tsHgYF: '2e8b57',\n\tsHshell: 'fff5ee',\n\tsiFna: 'a0522d',\n\tsilver: 'c0c0c0',\n\tskyXe: '87ceeb',\n\tUXe: '6a5acd',\n\tUWay: '708090',\n\tUgYy: '708090',\n\tsnow: 'fffafa',\n\tsprRggYF: 'ff7f',\n\tstAlXe: '4682b4',\n\ttan: 'd2b48c',\n\tteO: '8080',\n\ttEstN: 'd8bfd8',\n\ttomato: 'ff6347',\n\tQe: '40e0d0',\n\tviTet: 'ee82ee',\n\tJHt: 'f5deb3',\n\twEte: 'ffffff',\n\twEtesmoke: 'f5f5f5',\n\tLw: 'ffff00',\n\tLwgYF: '9acd32'\n};\nexport default function unpack() {\n  const unpacked = {};\n  const keys = Object.keys(names);\n  const tkeys = Object.keys(map);\n  let i, j, k, ok, nk;\n  for (i = 0; i < keys.length; i++) {\n    ok = nk = keys[i];\n    for (j = 0; j < tkeys.length; j++) {\n      k = tkeys[j];\n      nk = nk.replace(k, map[k]);\n    }\n    k = parseInt(names[ok], 16);\n    unpacked[nk] = [k >> 16 & 0xFF, k >> 8 & 0xFF, k & 0xFF];\n  }\n  return unpacked;\n}\n", "/**\n * @packageDocumentation\n * @module utils\n */\n\nimport unpack from '../packed.js';\nlet names;\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * Parse color name\n * @param {string} str - the color name\n * @return {RGBA} - the color\n */\nexport function nameParse(str) {\n  if (!names) {\n    names = unpack();\n    names.transparent = [0, 0, 0, 0];\n  }\n  const a = names[str.toLowerCase()];\n  return a && {\n    r: a[0],\n    g: a[1],\n    b: a[2],\n    a: a.length === 4 ? a[3] : 255\n  };\n}\n", "/**\n * @packageDocumentation\n * @module utils\n */\n\nimport {b2n, lim, p2b} from './byte.js';\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * @hidden\n */\nconst RGB_RE = /^rgba?\\(\\s*([-+.\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?[\\s,]+([-+.e\\d]+)(%)?(?:[\\s,/]+([-+.e\\d]+)(%)?)?\\s*\\)$/;\n\n/**\n * Parse rgb(a) string to RGBA\n * @param {string} str - the rgb string\n * @returns {RGBA} - the parsed color\n */\nexport function rgbParse(str) {\n  const m = RGB_RE.exec(str);\n  let a = 255;\n  let r, g, b;\n\n  if (!m) {\n    return;\n  }\n\n  // r is undefined\n  if (m[7] !== r) {\n    const v = +m[7];\n    a = m[8] ? p2b(v) : lim(v * 255, 0, 255);\n  }\n\n  r = +m[1];\n  g = +m[3];\n  b = +m[5];\n  r = 255 & (m[2] ? p2b(r) : lim(r, 0, 255));\n  g = 255 & (m[4] ? p2b(g) : lim(g, 0, 255));\n  b = 255 & (m[6] ? p2b(b) : lim(b, 0, 255));\n\n  return {\n    r: r,\n    g: g,\n    b: b,\n    a: a\n  };\n}\n\n/**\n * Return rgb(a) string from color\n * @param {RGBA} v - the color\n */\nexport function rgbString(v) {\n  return v && (\n    v.a < 255\n      ? `rgba(${v.r}, ${v.g}, ${v.b}, ${b2n(v.a)})`\n      : `rgb(${v.r}, ${v.g}, ${v.b})`\n  );\n}\n", "import {b2n, n2b} from './byte.js';\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\nconst to = v => v <= 0.0031308 ? v * 12.92 : Math.pow(v, 1.0 / 2.4) * 1.055 - 0.055;\nconst from = v => v <= 0.04045 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);\n\n/**\n * @param {RGBA} rgb1 from color\n * @param {RGBA} rgb2 to color\n * @param {number} t 0..1\n * @returns {RGBA} interpolaced\n */\nexport function interpolate(rgb1, rgb2, t) {\n  const r = from(b2n(rgb1.r));\n  const g = from(b2n(rgb1.g));\n  const b = from(b2n(rgb1.b));\n  return {\n    r: n2b(to(r + t * (from(b2n(rgb2.r)) - r))),\n    g: n2b(to(g + t * (from(b2n(rgb2.g)) - g))),\n    b: n2b(to(b + t * (from(b2n(rgb2.b)) - b))),\n    a: rgb1.a + t * (rgb2.a - rgb1.a)\n  };\n}\n", "/**\n * @packageDocumentation\n * @module index\n */\n\nimport {b2n, n2b, round} from './byte.js';\nimport {hexParse, hexString} from './hex.js';\nimport {hsl2rgb, hslString, hueParse, rgb2hsl, rotate} from './hue.js';\nimport {nameParse} from './names.js';\nimport {rgbParse, rgbString} from './rgb.js';\nimport {interpolate} from './srgb.js';\n\n/**\n * @typedef {import('./index.js').RGBA} RGBA\n */\n\n/**\n * Modify HSL properties\n * @param {RGBA} v - the color\n * @param {number} i - index [0=h, 1=s, 2=l]\n * @param {number} ratio - ratio [0..1]\n * @hidden\n */\nfunction modHSL(v, i, ratio) {\n  if (v) {\n    let tmp = rgb2hsl(v);\n    tmp[i] = Math.max(0, Math.min(tmp[i] + tmp[i] * ratio, i === 0 ? 360 : 1));\n    tmp = hsl2rgb(tmp);\n    v.r = tmp[0];\n    v.g = tmp[1];\n    v.b = tmp[2];\n  }\n}\n\n/**\n * Clone color\n * @param {RGBA} v - the color\n * @param {object} [proto] - prototype\n * @hidden\n */\nfunction clone(v, proto) {\n  return v ? Object.assign(proto || {}, v) : v;\n}\n\n/**\n * @param {RGBA|number[]} input\n * @hidden\n */\nfunction fromObject(input) {\n  var v = {r: 0, g: 0, b: 0, a: 255};\n  if (Array.isArray(input)) {\n    if (input.length >= 3) {\n      v = {r: input[0], g: input[1], b: input[2], a: 255};\n      if (input.length > 3) {\n        v.a = n2b(input[3]);\n      }\n    }\n  } else {\n    v = clone(input, {r: 0, g: 0, b: 0, a: 1});\n    v.a = n2b(v.a);\n  }\n  return v;\n}\n\n/**\n * @param {string} str\n * @hidden\n */\nfunction functionParse(str) {\n  if (str.charAt(0) === 'r') {\n    return rgbParse(str);\n  }\n  return hueParse(str);\n}\n\nexport default class Color {\n  /**\n   * constructor\n   * @param {Color|RGBA|string|number[]} input\n   */\n  constructor(input) {\n    if (input instanceof Color) {\n      return input;\n    }\n    const type = typeof input;\n    let v;\n    if (type === 'object') {\n      // @ts-ignore\n      v = fromObject(input);\n    } else if (type === 'string') {\n      // @ts-ignore\n      v = hexParse(input) || nameParse(input) || functionParse(input);\n    }\n\n    /** @type {RGBA} */\n    this._rgb = v;\n    /** @type {boolean} */\n    this._valid = !!v;\n  }\n\n  /**\n   * `true` if this is a valid color\n   * @returns {boolean}\n   */\n  get valid() {\n    return this._valid;\n  }\n\n  /**\n   * @returns {RGBA} - the color\n   */\n  get rgb() {\n    var v = clone(this._rgb);\n    if (v) {\n      v.a = b2n(v.a);\n    }\n    return v;\n  }\n\n  /**\n   * @param {RGBA} obj - the color\n   */\n  set rgb(obj) {\n    this._rgb = fromObject(obj);\n  }\n\n  /**\n   * rgb(a) string\n   * @return {string|undefined}\n   */\n  rgbString() {\n    return this._valid ? rgbString(this._rgb) : undefined;\n  }\n\n  /**\n   * hex string\n   * @return {string|undefined}\n   */\n  hexString() {\n    return this._valid ? hexString(this._rgb) : undefined;\n  }\n\n  /**\n   * hsl(a) string\n   * @return {string|undefined}\n   */\n  hslString() {\n    return this._valid ? hslString(this._rgb) : undefined;\n  }\n\n  /**\n   * Mix another color to this color.\n   * @param {Color} color - Color to mix in\n   * @param {number} weight - 0..1\n   * @returns {Color}\n   */\n  mix(color, weight) {\n    if (color) {\n      const c1 = this.rgb;\n      const c2 = color.rgb;\n      let w2; // using instead of undefined in the next line\n      const p = weight === w2 ? 0.5 : weight;\n      const w = 2 * p - 1;\n      const a = c1.a - c2.a;\n      const w1 = ((w * a === -1 ? w : (w + a) / (1 + w * a)) + 1) / 2.0;\n      w2 = 1 - w1;\n      c1.r = 0xFF & w1 * c1.r + w2 * c2.r + 0.5;\n      c1.g = 0xFF & w1 * c1.g + w2 * c2.g + 0.5;\n      c1.b = 0xFF & w1 * c1.b + w2 * c2.b + 0.5;\n      c1.a = p * c1.a + (1 - p) * c2.a;\n      this.rgb = c1;\n    }\n    return this;\n  }\n\n  /**\n   * Interpolate a color value between this and `color`\n   * @param {Color} color\n   * @param {number} t - 0..1\n   * @returns {Color}\n   */\n  interpolate(color, t) {\n    if (color) {\n      this._rgb = interpolate(this._rgb, color._rgb, t);\n    }\n    return this;\n  }\n\n  /**\n   * Clone\n   * @returns {Color}\n   */\n  clone() {\n    return new Color(this.rgb);\n  }\n\n  /**\n   * Set aplha\n   * @param {number} a - the alpha [0..1]\n   * @returns {Color}\n   */\n  alpha(a) {\n    this._rgb.a = n2b(a);\n    return this;\n  }\n\n  /**\n   * Make clearer\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  clearer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 - ratio;\n    return this;\n  }\n\n  /**\n   * Convert to grayscale\n   * @returns {Color}\n   */\n  greyscale() {\n    const rgb = this._rgb;\n    // http://en.wikipedia.org/wiki/Grayscale#Converting_color_to_grayscale\n    const val = round(rgb.r * 0.3 + rgb.g * 0.59 + rgb.b * 0.11);\n    rgb.r = rgb.g = rgb.b = val;\n    return this;\n  }\n\n  /**\n   * Opaquer\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  opaquer(ratio) {\n    const rgb = this._rgb;\n    rgb.a *= 1 + ratio;\n    return this;\n  }\n\n  /**\n   * Negates the rgb value\n   * @returns {Color}\n   */\n  negate() {\n    const v = this._rgb;\n    v.r = 255 - v.r;\n    v.g = 255 - v.g;\n    v.b = 255 - v.b;\n    return this;\n  }\n\n  /**\n   * Lighten\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  lighten(ratio) {\n    modHSL(this._rgb, 2, ratio);\n    return this;\n  }\n\n  /**\n   * Darken\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  darken(ratio) {\n    modHSL(this._rgb, 2, -ratio);\n    return this;\n  }\n\n  /**\n   * Saturate\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  saturate(ratio) {\n    modHSL(this._rgb, 1, ratio);\n    return this;\n  }\n\n  /**\n   * Desaturate\n   * @param {number} ratio - ratio [0..1]\n   * @returns {Color}\n   */\n  desaturate(ratio) {\n    modHSL(this._rgb, 1, -ratio);\n    return this;\n  }\n\n  /**\n   * Rotate\n   * @param {number} deg - degrees to rotate\n   * @returns {Color}\n   */\n  rotate(deg) {\n    rotate(this._rgb, deg);\n    return this;\n  }\n}\n", "/**\n * @packageDocumentation\n * @module index\n */\n\nimport Color from './color.js';\n\nexport {Color};\nexport * from './byte.js';\nexport * from './hex.js';\nexport * from './hue.js';\nexport * from './names.js';\nexport * from './rgb.js';\n\n/**\n * @typedef {Object} RGBA\n * @property {number} r - red [0..255]\n * @property {number} g - green [0..255]\n * @property {number} b - blue [0..255]\n * @property {number} a - alpha [0..1]\n * @internal\n */\n\n/**\n * Construct new Color instance\n * @param {Color|RGBA|string|number[]} input\n * @internal\n */\nexport default function(input) {\n  return new Color(input);\n}\n", "/**\n * @packageDocumentation\n * @module index\n */\n\nimport * as color from './index.esm.js';\n\nexport default Object.assign(color.default, color);\n"], "names": ["round", "v", "lim", "l", "h", "Math", "max", "min", "p2b", "n2b", "b2n", "n2p", "map", "A", "B", "C", "D", "E", "F", "a", "b", "c", "d", "e", "f", "hex", "h1", "h2", "eq", "hexParse", "str", "ret", "len", "length", "r", "g", "hexString", "isShort", "alpha", "undefined", "HUE_RE", "hsl2rgbn", "s", "n", "k", "hsv2rgbn", "hwb2rgbn", "w", "rgb", "i", "rgb2hsl", "hueValue", "calln", "Array", "isArray", "hsl2rgb", "hwb2rgb", "hsv2rgb", "hue", "hue<PERSON><PERSON><PERSON>", "m", "exec", "p1", "p2", "rotate", "deg", "hslString", "x", "Z", "Y", "X", "W", "V", "U", "T", "S", "R", "Q", "P", "O", "N", "M", "L", "K", "G", "H", "I", "J", "names", "OiceXe", "antiquewEte", "aqua", "aquamarRe", "azuY", "beige", "bisque", "black", "blan<PERSON>ed<PERSON><PERSON>", "Xe", "XeviTet", "bPwn", "burlywood", "caMtXe", "<PERSON><PERSON><PERSON><PERSON>", "KocTate", "cSO", "cSnflowerXe", "cSnsilk", "crimson", "cyan", "xXe", "xcyan", "xgTMnPd", "xWay", "xgYF", "xgYy", "xkhaki", "xmagFta", "xTivegYF", "xSange", "xScEd", "xYd", "xsOmon", "xsHgYF", "xUXe", "xUWay", "xUgYy", "xQe", "xviTet", "dAppRk", "dApskyXe", "dim<PERSON>ay", "dimgYy", "dodgerXe", "fiYbrick", "flSOwEte", "foYstWAn", "fuKsia", "gaRsbSo", "ghostwEte", "gTd", "gTMnPd", "Way", "gYF", "gYFLw", "gYy", "honeyMw", "hotpRk", "RdianYd", "Rdigo", "ivSy", "khaki", "lavFMr", "lavFMrXsh", "lawngYF", "NmoncEffon", "ZXe", "ZcSO", "<PERSON><PERSON><PERSON>", "ZgTMnPdLw", "ZWay", "ZgYF", "ZgYy", "ZpRk", "ZsOmon", "ZsHgYF", "ZskyXe", "ZUWay", "ZUgYy", "ZstAlXe", "ZLw", "lime", "limegYF", "lRF", "magFta", "ma<PERSON><PERSON>", "VaquamarRe", "VXe", "VScEd", "VpurpN", "VsHgYF", "VUXe", "VsprRggYF", "VQe", "VviTetYd", "midnightXe", "mRtcYam", "misty<PERSON>e", "moccasR", "navajowEte", "navy", "Tdlace", "Tive", "TivedBb", "<PERSON><PERSON>", "SangeYd", "ScEd", "pOegTMnPd", "pOegYF", "pOeQe", "pOeviTetYd", "papayawEp", "pHKpuff", "peru", "pRk", "plum", "powMrXe", "purpN", "YbeccapurpN", "Yd", "Psybrown", "PyOXe", "saddNbPwn", "sOmon", "sandybPwn", "sHgYF", "sHshell", "siFna", "silver", "skyXe", "UXe", "UWay", "UgYy", "snow", "sprRggYF", "stAlXe", "tan", "teO", "tEstN", "tomato", "Qe", "viTet", "JHt", "wEte", "wEtesmoke", "Lw", "LwgYF", "nameParse", "unpacked", "keys", "Object", "tkeys", "j", "ok", "nk", "replace", "parseInt", "unpack", "transparent", "toLowerCase", "RGB_RE", "rgbParse", "rgbString", "to", "pow", "from", "modHSL", "ratio", "tmp", "clone", "proto", "assign", "fromObject", "input", "Color", "constructor", "type", "char<PERSON>t", "this", "_rgb", "_valid", "valid", "obj", "mix", "color", "weight", "c1", "c2", "w2", "p", "w1", "interpolate", "t", "rgb1", "rgb2", "clearer", "greyscale", "val", "opaquer", "negate", "lighten", "darken", "saturate", "desaturate", "index_esm", "color.default"], "mappings": ";;;;;;iPASO,SAASA,EAAMC,GACpB,OAAOA,EAAI,GAAM,CACnB,CAEO,MAAMC,EAAM,CAACD,EAAGE,EAAGC,IAAMC,KAAKC,IAAID,KAAKE,IAAIN,EAAGG,GAAID,GAMlD,SAASK,EAAIP,GAClB,OAAOC,EAAIF,EAAU,KAAJC,GAAW,EAAG,IACjC,CAcO,SAASQ,EAAIR,GAClB,OAAOC,EAAIF,EAAU,IAAJC,GAAU,EAAG,IAChC,CAMO,SAASS,EAAIT,GAClB,OAAOC,EAAIF,EAAMC,EAAI,MAAQ,IAAK,EAAG,EACvC,CAMO,SAASU,EAAIV,GAClB,OAAOC,EAAIF,EAAU,IAAJC,GAAU,EAAG,IAChC,CCzCA,MAAMW,EAAM,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,EAAGC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,GAAIC,EAAG,IAKnJC,EAAM,IAAI,oBAMVC,EAAKN,GAAKK,EAAQ,GAAJL,GAMdO,EAAKP,GAAKK,GAAS,IAAJL,IAAa,GAAKK,EAAQ,GAAJL,GAMrCQ,EAAKR,IAAW,IAAJA,IAAa,IAAY,GAAJA,GAYhC,SAASS,EAASC,GACvB,IACIC,EADAC,EAAMF,EAAIG,OAmBd,MAjBe,MAAXH,EAAI,KACM,IAARE,GAAqB,IAARA,EACfD,EAAM,CACJG,EAAG,IAAoB,GAAdtB,EAAIkB,EAAI,IACjBK,EAAG,IAAoB,GAAdvB,EAAIkB,EAAI,IACjBV,EAAG,IAAoB,GAAdR,EAAIkB,EAAI,IACjBX,EAAW,IAARa,EAA0B,GAAdpB,EAAIkB,EAAI,IAAW,KAEnB,IAARE,GAAqB,IAARA,IACtBD,EAAM,CACJG,EAAGtB,EAAIkB,EAAI,KAAO,EAAIlB,EAAIkB,EAAI,IAC9BK,EAAGvB,EAAIkB,EAAI,KAAO,EAAIlB,EAAIkB,EAAI,IAC9BV,EAAGR,EAAIkB,EAAI,KAAO,EAAIlB,EAAIkB,EAAI,IAC9BX,EAAW,IAARa,EAAapB,EAAIkB,EAAI,KAAO,EAAIlB,EAAIkB,EAAI,IAAO,OAIjDC,CACT,CASO,SAASK,EAAUnC,GACxB,IAAIuB,EArCUvB,IAAK2B,EAAG3B,EAAEiC,IAAMN,EAAG3B,EAAEkC,IAAMP,EAAG3B,EAAEmB,IAAMQ,EAAG3B,EAAEkB,GAqCjDkB,CAAQpC,GAAKyB,EAAKC,EAC1B,OAAO1B,EACH,IAAMuB,EAAEvB,EAAEiC,GAAKV,EAAEvB,EAAEkC,GAAKX,EAAEvB,EAAEmB,GAVpB,EAACD,EAAGK,IAAML,EAAI,IAAMK,EAAEL,GAAK,GAUFmB,CAAMrC,EAAEkB,EAAGK,QAC5Ce,CACN,CCpEA,MAAMC,EAAS,+GAWf,SAASC,EAASrC,EAAGsC,EAAGvC,GACtB,MAAMgB,EAAIuB,EAAIrC,KAAKE,IAAIJ,EAAG,EAAIA,GAIxBqB,EAAI,CAACmB,EAAGC,GAAKD,EAAIvC,EAAI,IAAM,KAAOD,EAAIgB,EAAId,KAAKC,IAAID,KAAKE,IAAIqC,EAAI,EAAG,EAAIA,EAAG,IAAK,GACrF,MAAO,CAACpB,EAAE,GAAIA,EAAE,GAAIA,EAAE,GACxB,CAWA,SAASqB,EAASzC,EAAGsC,EAAGzC,GAItB,MAAMuB,EAAI,CAACmB,EAAGC,GAAKD,EAAIvC,EAAI,IAAM,IAAMH,EAAIA,EAAIyC,EAAIrC,KAAKC,IAAID,KAAKE,IAAIqC,EAAG,EAAIA,EAAG,GAAI,GACnF,MAAO,CAACpB,EAAE,GAAIA,EAAE,GAAIA,EAAE,GACxB,CAUA,SAASsB,EAAS1C,EAAG2C,EAAG3B,GACtB,MAAM4B,EAAMP,EAASrC,EAAG,EAAG,IAC3B,IAAI6C,EAMJ,IALIF,EAAI3B,EAAI,IACV6B,EAAI,GAAKF,EAAI3B,GACb2B,GAAKE,EACL7B,GAAK6B,GAEFA,EAAI,EAAGA,EAAI,EAAGA,IACjBD,EAAIC,IAAM,EAAIF,EAAI3B,EAClB4B,EAAIC,IAAMF,EAEZ,OAAOC,CACT,CAiBO,SAASE,EAAQjD,GACtB,MACMiC,EAAIjC,EAAEiC,EADE,IAERC,EAAIlC,EAAEkC,EAFE,IAGRf,EAAInB,EAAEmB,EAHE,IAIRd,EAAMD,KAAKC,IAAI4B,EAAGC,EAAGf,GACrBb,EAAMF,KAAKE,IAAI2B,EAAGC,EAAGf,GACrBjB,GAAKG,EAAMC,GAAO,EACxB,IAAIH,EAAGsC,EAAGpB,EAOV,OANIhB,IAAQC,IACVe,EAAIhB,EAAMC,EACVmC,EAAIvC,EAAI,GAAMmB,GAAK,EAAIhB,EAAMC,GAAOe,GAAKhB,EAAMC,GAC/CH,EA3BJ,SAAkB8B,EAAGC,EAAGf,EAAGE,EAAGhB,GAC5B,OAAI4B,IAAM5B,GACC6B,EAAIf,GAAKE,GAAMa,EAAIf,EAAI,EAAI,GAElCe,IAAM7B,GACAc,EAAIc,GAAKZ,EAAI,GAEfY,EAAIC,GAAKb,EAAI,CACvB,CAmBQ6B,CAASjB,EAAGC,EAAGf,EAAGE,EAAGhB,GACzBF,EAAQ,GAAJA,EAAS,IAER,CAAK,EAAJA,EAAOsC,GAAK,EAAGvC,EACzB,CAUA,SAASiD,EAAM5B,EAAGL,EAAGC,EAAGC,GACtB,OACEgC,MAAMC,QAAQnC,GACVK,EAAEL,EAAE,GAAIA,EAAE,GAAIA,EAAE,IAChBK,EAAEL,EAAGC,EAAGC,IACZT,IAAIH,EACR,CASO,SAAS8C,EAAQnD,EAAGsC,EAAGvC,GAC5B,OAAOiD,EAAMX,EAAUrC,EAAGsC,EAAGvC,EAC/B,CASO,SAASqD,EAAQpD,EAAG2C,EAAG3B,GAC5B,OAAOgC,EAAMN,EAAU1C,EAAG2C,EAAG3B,EAC/B,CASO,SAASqC,EAAQrD,EAAGsC,EAAGzC,GAC5B,OAAOmD,EAAMP,EAAUzC,EAAGsC,EAAGzC,EAC/B,CAMA,SAASyD,EAAItD,GACX,OAAQA,EAAI,IAAM,KAAO,GAC3B,CAOO,SAASuD,EAAS7B,GACvB,MAAM8B,EAAIpB,EAAOqB,KAAK/B,GACtB,IACI7B,EADAkB,EAAI,IAER,IAAKyC,EACH,OAGEA,EAAE,KAAO3D,IACXkB,EAAIyC,EAAE,GAAKpD,GAAKoD,EAAE,IAAMnD,GAAKmD,EAAE,KAEjC,MAAMxD,EAAIsD,GAAKE,EAAE,IACXE,GAAMF,EAAE,GAAK,IACbG,GAAMH,EAAE,GAAK,IAQnB,OANE3D,EADW,QAAT2D,EAAE,GACAJ,EAAQpD,EAAG0D,EAAIC,GACD,QAATH,EAAE,GACPH,EAAQrD,EAAG0D,EAAIC,GAEfR,EAAQnD,EAAG0D,EAAIC,GAEd,CACL7B,EAAGjC,EAAE,GACLkC,EAAGlC,EAAE,GACLmB,EAAGnB,EAAE,GACLkB,EAAGA,EAEP,CAOO,SAAS6C,EAAO/D,EAAGgE,GACxB,IAAI7D,EAAI8C,EAAQjD,GAChBG,EAAE,GAAKsD,EAAItD,EAAE,GAAK6D,GAClB7D,EAAImD,EAAQnD,GACZH,EAAEiC,EAAI9B,EAAE,GACRH,EAAEkC,EAAI/B,EAAE,GACRH,EAAEmB,EAAIhB,EAAE,EACV,CAOO,SAAS8D,EAAUjE,GACxB,IAAKA,EACH,OAEF,MAAMkB,EAAI+B,EAAQjD,GACZG,EAAIe,EAAE,GACNuB,EAAI/B,EAAIQ,EAAE,IACVhB,EAAIQ,EAAIQ,EAAE,IAChB,OAAOlB,EAAEkB,EAAI,IACT,QAAQf,MAAMsC,OAAOvC,OAAOO,EAAIT,EAAEkB,MAClC,OAAOf,MAAMsC,OAAOvC,KAC1B,CCnOA,MAAMS,EAAM,CACXuD,EAAG,OACHC,EAAG,QACHC,EAAG,KACHC,EAAG,MACHC,EAAG,KACHC,EAAG,SACHC,EAAG,QACH5D,EAAG,KACH6D,EAAG,KACHC,EAAG,KACH7D,EAAG,KACHC,EAAG,QACHC,EAAG,QACH4D,EAAG,KACHC,EAAG,WACH5D,EAAG,KACH6D,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,KACHC,EAAG,QACHhE,EAAG,KACHiE,EAAG,KACHC,EAAG,OACHC,EAAG,KACHC,EAAG,QACHC,EAAG,MAEEC,EAAQ,CACbC,OAAQ,SACRC,YAAa,SACbC,KAAM,OACNC,UAAW,SACXC,KAAM,SACNC,MAAO,SACPC,OAAQ,SACRC,MAAO,IACPC,aAAc,SACdC,GAAI,KACJC,QAAS,SACTC,KAAM,SACNC,UAAW,SACXC,OAAQ,SACRC,SAAU,SACVC,QAAS,SACTC,IAAK,SACLC,YAAa,SACbC,QAAS,SACTC,QAAS,SACTC,KAAM,OACNC,IAAK,KACLC,MAAO,OACPC,QAAS,SACTC,KAAM,SACNC,KAAM,OACNC,KAAM,SACNC,OAAQ,SACRC,QAAS,SACTC,SAAU,SACVC,OAAQ,SACRC,MAAO,SACPC,IAAK,SACLC,OAAQ,SACRC,OAAQ,SACRC,KAAM,SACNC,MAAO,SACPC,MAAO,SACPC,IAAK,OACLC,OAAQ,SACRC,OAAQ,SACRC,SAAU,OACVC,OAAQ,SACRC,OAAQ,SACRC,SAAU,SACVC,SAAU,SACVC,SAAU,SACVC,SAAU,SACVC,OAAQ,SACRC,QAAS,SACTC,UAAW,SACXC,IAAK,SACLC,OAAQ,SACRC,IAAK,SACLC,IAAK,OACLC,MAAO,SACPC,IAAK,SACLC,QAAS,SACTC,OAAQ,SACRC,QAAS,SACTC,MAAO,SACPC,KAAM,SACNC,MAAO,SACPC,OAAQ,SACRC,UAAW,SACXC,QAAS,SACTC,WAAY,SACZC,IAAK,SACLC,KAAM,SACNC,MAAO,SACPC,UAAW,SACXC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,OAAQ,SACRC,OAAQ,SACRC,OAAQ,SACRC,MAAO,SACPC,MAAO,SACPC,QAAS,SACTC,IAAK,SACLC,KAAM,OACNC,QAAS,SACTC,IAAK,SACLC,OAAQ,SACRC,MAAO,SACPC,WAAY,SACZC,IAAK,KACLC,MAAO,SACPC,OAAQ,SACRC,OAAQ,SACRC,KAAM,SACNC,UAAW,OACXC,IAAK,SACLC,SAAU,SACVC,WAAY,SACZC,QAAS,SACTC,SAAU,SACVC,QAAS,SACTC,WAAY,SACZC,KAAM,KACNC,OAAQ,SACRC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,QAAS,SACTC,KAAM,SACNC,UAAW,SACXC,OAAQ,SACRC,MAAO,SACPC,WAAY,SACZC,UAAW,SACXC,QAAS,SACTC,KAAM,SACNC,IAAK,SACLC,KAAM,SACNC,QAAS,SACTC,MAAO,SACPC,YAAa,SACbC,GAAI,SACJC,SAAU,SACVC,MAAO,SACPC,UAAW,SACXC,MAAO,SACPC,UAAW,SACXC,MAAO,SACPC,QAAS,SACTC,MAAO,SACPC,OAAQ,SACRC,MAAO,SACPC,IAAK,SACLC,KAAM,SACNC,KAAM,SACNC,KAAM,SACNC,SAAU,OACVC,OAAQ,SACRC,IAAK,SACLC,IAAK,OACLC,MAAO,SACPC,OAAQ,SACRC,GAAI,SACJC,MAAO,SACPC,IAAK,SACLC,KAAM,SACNC,UAAW,SACXC,GAAI,SACJC,MAAO,UC5KR,IAAIpJ,EAWG,SAASqJ,EAAU/M,GACnB0D,IACHA,EDiKW,WACb,MAAMsJ,EAAW,CAAE,EACbC,EAAOC,OAAOD,KAAKvJ,GACnByJ,EAAQD,OAAOD,KAAKnO,GAC1B,IAAIqC,EAAGiM,EAAGtM,EAAGuM,EAAIC,EACjB,IAAKnM,EAAI,EAAGA,EAAI8L,EAAK9M,OAAQgB,IAAK,CAEhC,IADAkM,EAAKC,EAAKL,EAAK9L,GACViM,EAAI,EAAGA,EAAID,EAAMhN,OAAQiN,IAC5BtM,EAAIqM,EAAMC,GACVE,EAAKA,EAAGC,QAAQzM,EAAGhC,EAAIgC,IAEzBA,EAAI0M,SAAS9J,EAAM2J,GAAK,IACxBL,EAASM,GAAM,CAACxM,GAAK,GAAK,IAAMA,GAAK,EAAI,IAAU,IAAJA,EACnD,CACE,OAAOkM,CACT,CChLYS,GACR/J,EAAMgK,YAAc,CAAC,EAAG,EAAG,EAAG,IAEhC,MAAMrO,EAAIqE,EAAM1D,EAAI2N,eACpB,OAAOtO,GAAK,CACVe,EAAGf,EAAE,GACLgB,EAAGhB,EAAE,GACLC,EAAGD,EAAE,GACLA,EAAgB,IAAbA,EAAEc,OAAed,EAAE,GAAK,IAE/B,CCfA,MAAMuO,EAAS,uGAOR,SAASC,EAAS7N,GACvB,MAAM8B,EAAI8L,EAAO7L,KAAK/B,GACtB,IACII,EAAGC,EAAGf,EADND,EAAI,IAGR,GAAKyC,EAAL,CAKA,GAAIA,EAAE,KAAO1B,EAAG,CACd,MAAMjC,GAAK2D,EAAE,GACbzC,EAAIyC,EAAE,GAAKpD,EAAIP,GAAKC,EAAQ,IAAJD,EAAS,EAAG,IACxC,CASE,OAPAiC,GAAK0B,EAAE,GACPzB,GAAKyB,EAAE,GACPxC,GAAKwC,EAAE,GACP1B,EAAI,KAAO0B,EAAE,GAAKpD,EAAI0B,GAAKhC,EAAIgC,EAAG,EAAG,MACrCC,EAAI,KAAOyB,EAAE,GAAKpD,EAAI2B,GAAKjC,EAAIiC,EAAG,EAAG,MACrCf,EAAI,KAAOwC,EAAE,GAAKpD,EAAIY,GAAKlB,EAAIkB,EAAG,EAAG,MAE9B,CACLc,EAAGA,EACHC,EAAGA,EACHf,EAAGA,EACHD,EAAGA,EAnBP,CAqBA,CAMO,SAASyO,EAAU3P,GACxB,OAAOA,IACLA,EAAEkB,EAAI,IACF,QAAQlB,EAAEiC,MAAMjC,EAAEkC,MAAMlC,EAAEmB,MAAMV,EAAIT,EAAEkB,MACtC,OAAOlB,EAAEiC,MAAMjC,EAAEkC,MAAMlC,EAAEmB,KAEjC,CCvDA,MAAMyO,EAAK5P,GAAKA,GAAK,SAAgB,MAAJA,EAAqC,MAAzBI,KAAKyP,IAAI7P,EAAG,EAAM,KAAe,KACxE8P,EAAO9P,GAAKA,GAAK,OAAUA,EAAI,MAAQI,KAAKyP,KAAK7P,EAAI,MAAS,MAAO,KCgB3E,SAAS+P,EAAO/P,EAAGgD,EAAGgN,GACpB,GAAIhQ,EAAG,CACL,IAAIiQ,EAAMhN,EAAQjD,GAClBiQ,EAAIjN,GAAK5C,KAAKC,IAAI,EAAGD,KAAKE,IAAI2P,EAAIjN,GAAKiN,EAAIjN,GAAKgN,EAAa,IAANhN,EAAU,IAAM,IACvEiN,EAAM3M,EAAQ2M,GACdjQ,EAAEiC,EAAIgO,EAAI,GACVjQ,EAAEkC,EAAI+N,EAAI,GACVjQ,EAAEmB,EAAI8O,EAAI,EACd,CACA,CAQA,SAASC,EAAMlQ,EAAGmQ,GAChB,OAAOnQ,EAAI+O,OAAOqB,OAAOD,GAAS,CAAE,EAAEnQ,GAAKA,CAC7C,CAMA,SAASqQ,EAAWC,GAClB,IAAItQ,EAAI,CAACiC,EAAG,EAAGC,EAAG,EAAGf,EAAG,EAAGD,EAAG,KAY9B,OAXIkC,MAAMC,QAAQiN,GACZA,EAAMtO,QAAU,IAClBhC,EAAI,CAACiC,EAAGqO,EAAM,GAAIpO,EAAGoO,EAAM,GAAInP,EAAGmP,EAAM,GAAIpP,EAAG,KAC3CoP,EAAMtO,OAAS,IACjBhC,EAAEkB,EAAIV,EAAI8P,EAAM,OAIpBtQ,EAAIkQ,EAAMI,EAAO,CAACrO,EAAG,EAAGC,EAAG,EAAGf,EAAG,EAAGD,EAAG,KACrCA,EAAIV,EAAIR,EAAEkB,GAEPlB,CACT,CAae,MAAMuQ,EAKnB,WAAAC,CAAYF,GACV,GAAIA,aAAiBC,EACnB,OAAOD,EAET,MAAMG,SAAcH,EACpB,IAAItQ,EAjBR,IAAuB6B,EAkBN,WAAT4O,EAEFzQ,EAAIqQ,EAAWC,GACG,WAATG,IAETzQ,EAAI4B,EAAS0O,IAAU1B,EAAU0B,KAtBf,OADDzO,EAuBwCyO,GAtBrDI,OAAO,GACNhB,EAAS7N,GAEX6B,EAAS7B,KAuBd8O,KAAKC,KAAO5Q,EAEZ2Q,KAAKE,SAAW7Q,CACpB,CAME,SAAI8Q,GACF,OAAOH,KAAKE,MAChB,CAKE,OAAI9N,GACF,IAAI/C,EAAIkQ,EAAMS,KAAKC,MAInB,OAHI5Q,IACFA,EAAEkB,EAAIT,EAAIT,EAAEkB,IAEPlB,CACX,CAKE,OAAI+C,CAAIgO,GACNJ,KAAKC,KAAOP,EAAWU,EAC3B,CAME,SAAApB,GACE,OAAOgB,KAAKE,OAASlB,EAAUgB,KAAKC,WAAQtO,CAChD,CAME,SAAAH,GACE,OAAOwO,KAAKE,OAAS1O,EAAUwO,KAAKC,WAAQtO,CAChD,CAME,SAAA2B,GACE,OAAO0M,KAAKE,OAAS5M,EAAU0M,KAAKC,WAAQtO,CAChD,CAQE,GAAA0O,CAAIC,EAAOC,GACT,GAAID,EAAO,CACT,MAAME,EAAKR,KAAK5N,IACVqO,EAAKH,EAAMlO,IACjB,IAAIsO,EACJ,MAAMC,EAAIJ,IAAWG,EAAK,GAAMH,EAC1BpO,EAAI,EAAIwO,EAAI,EACZpQ,EAAIiQ,EAAGjQ,EAAIkQ,EAAGlQ,EACdqQ,IAAOzO,EAAI5B,IAAO,EAAI4B,GAAKA,EAAI5B,IAAM,EAAI4B,EAAI5B,IAAM,GAAK,EAC9DmQ,EAAK,EAAIE,EACTJ,EAAGlP,EAAI,IAAOsP,EAAKJ,EAAGlP,EAAIoP,EAAKD,EAAGnP,EAAI,GACtCkP,EAAGjP,EAAI,IAAOqP,EAAKJ,EAAGjP,EAAImP,EAAKD,EAAGlP,EAAI,GACtCiP,EAAGhQ,EAAI,IAAOoQ,EAAKJ,EAAGhQ,EAAIkQ,EAAKD,EAAGjQ,EAAI,GACtCgQ,EAAGjQ,EAAIoQ,EAAIH,EAAGjQ,GAAK,EAAIoQ,GAAKF,EAAGlQ,EAC/ByP,KAAK5N,IAAMoO,CACjB,CACI,OAAOR,IACX,CAQE,WAAAa,CAAYP,EAAOQ,GAIjB,OAHIR,IACFN,KAAKC,KDxKJ,SAAqBc,EAAMC,EAAMF,GACtC,MAAMxP,EAAI6N,EAAKrP,EAAIiR,EAAKzP,IAClBC,EAAI4N,EAAKrP,EAAIiR,EAAKxP,IAClBf,EAAI2O,EAAKrP,EAAIiR,EAAKvQ,IACxB,MAAO,CACLc,EAAGzB,EAAIoP,EAAG3N,EAAIwP,GAAK3B,EAAKrP,EAAIkR,EAAK1P,IAAMA,KACvCC,EAAG1B,EAAIoP,EAAG1N,EAAIuP,GAAK3B,EAAKrP,EAAIkR,EAAKzP,IAAMA,KACvCf,EAAGX,EAAIoP,EAAGzO,EAAIsQ,GAAK3B,EAAKrP,EAAIkR,EAAKxQ,IAAMA,KACvCD,EAAGwQ,EAAKxQ,EAAIuQ,GAAKE,EAAKzQ,EAAIwQ,EAAKxQ,GAEnC,CC8JkBsQ,CAAYb,KAAKC,KAAMK,EAAML,KAAMa,IAE1Cd,IACX,CAME,KAAAT,GACE,OAAO,IAAIK,EAAMI,KAAK5N,IAC1B,CAOE,KAAAV,CAAMnB,GAEJ,OADAyP,KAAKC,KAAK1P,EAAIV,EAAIU,GACXyP,IACX,CAOE,OAAAiB,CAAQ5B,GAGN,OAFYW,KAAKC,KACb1P,GAAK,EAAI8O,EACNW,IACX,CAME,SAAAkB,GACE,MAAM9O,EAAM4N,KAAKC,KAEXkB,EAAM/R,EAAc,GAARgD,EAAId,EAAkB,IAARc,EAAIb,EAAmB,IAARa,EAAI5B,GAEnD,OADA4B,EAAId,EAAIc,EAAIb,EAAIa,EAAI5B,EAAI2Q,EACjBnB,IACX,CAOE,OAAAoB,CAAQ/B,GAGN,OAFYW,KAAKC,KACb1P,GAAK,EAAI8O,EACNW,IACX,CAME,MAAAqB,GACE,MAAMhS,EAAI2Q,KAAKC,KAIf,OAHA5Q,EAAEiC,EAAI,IAAMjC,EAAEiC,EACdjC,EAAEkC,EAAI,IAAMlC,EAAEkC,EACdlC,EAAEmB,EAAI,IAAMnB,EAAEmB,EACPwP,IACX,CAOE,OAAAsB,CAAQjC,GAEN,OADAD,EAAOY,KAAKC,KAAM,EAAGZ,GACdW,IACX,CAOE,MAAAuB,CAAOlC,GAEL,OADAD,EAAOY,KAAKC,KAAM,GAAIZ,GACfW,IACX,CAOE,QAAAwB,CAASnC,GAEP,OADAD,EAAOY,KAAKC,KAAM,EAAGZ,GACdW,IACX,CAOE,UAAAyB,CAAWpC,GAET,OADAD,EAAOY,KAAKC,KAAM,GAAIZ,GACfW,IACX,CAOE,MAAA5M,CAAOC,GAEL,OADAD,EAAO4M,KAAKC,KAAM5M,GACX2M,IACX,EChRe,SAAQ0B,EAAC/B,GACtB,OAAO,IAAIC,EAAMD,EACnB,uDRHO,SAAatQ,GAClB,OAAOC,EAAIF,EAAMC,EAAI,MAAO,EAAG,IACjC,uLStBe+O,OAAOqB,OAAOkC,EAAerB"}