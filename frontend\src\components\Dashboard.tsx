import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Dashboard: React.FC = () => {
  const { state, logout } = useAuth();

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">MoneyMind</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-700">
                Welcome, {state.user?.firstName} {state.user?.lastName}!
              </span>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="border-4 border-dashed border-gray-200 rounded-lg h-96 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Welcome to MoneyMind Dashboard!
              </h2>
              <p className="text-gray-600 mb-4">
                Your personal finance tracker is ready to help you manage your money.
              </p>
              <div className="bg-white p-6 rounded-lg shadow-md max-w-md mx-auto">
                <h3 className="text-lg font-semibold mb-2">User Information</h3>
                <div className="text-left space-y-2">
                  <p><strong>Username:</strong> {state.user?.username}</p>
                  <p><strong>Email:</strong> {state.user?.email}</p>
                  <p><strong>Name:</strong> {state.user?.firstName} {state.user?.lastName}</p>
                  <p><strong>Member since:</strong> {state.user?.createdAt ? new Date(state.user.createdAt).toLocaleDateString() : 'N/A'}</p>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-4">
                More features coming soon in Sprint 2!
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
